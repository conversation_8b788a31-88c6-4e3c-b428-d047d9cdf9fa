{"name": "entererp", "version": "1.0.0", "description": "Real-time application framework.", "author": "EnterSoft <<EMAIL>>", "private": true, "scripts": {"dev": "./kit dev", "build": "./kit build"}, "dependencies": {"@ag-grid-community/all-modules": "24.1.0", "@ag-grid-community/vue": "24.1.1", "@ag-grid-enterprise/all-modules": "24.1.0", "@amcharts/amcharts4": "4.10.36", "@aws-sdk/client-s3": "^3.540.0", "@cubejs-backend/postgres-driver": "0.31.69", "@cubejs-backend/server-core": "0.31.69", "@cubejs-client/core": "0.31.63", "@feathers-plus/batch-loader": "0.3.6", "@feathersjs/authentication": "4.5.15", "@feathersjs/authentication-client": "4.5.15", "@feathersjs/authentication-local": "4.5.15", "@feathersjs/commons": "4.5.15", "@feathersjs/errors": "4.5.15", "@feathersjs/express": "4.5.15", "@feathersjs/feathers": "4.5.15", "@feathersjs/transport-commons": "4.5.15", "@fullcalendar/core": "4.4.2", "@fullcalendar/daygrid": "4.4.2", "@fullcalendar/interaction": "4.4.2", "@fullcalendar/list": "4.4.2", "@fullcalendar/luxon": "4.4.2", "@fullcalendar/rrule": "4.4.2", "@fullcalendar/timegrid": "4.4.2", "@google/maps": "1.1.3", "@hokify/agenda": "6.3.0", "@interactjs/actions": "1.10.17", "@interactjs/auto-start": "1.10.17", "@interactjs/dev-tools": "1.10.17", "@interactjs/interactjs": "1.10.17", "@interactjs/modifiers": "1.10.17", "@monaco-editor/loader": "1.3.3", "@riophae/vue-treeselect": "0.4.0", "@xmldom/xmldom": "0.8.10", "archiver": "5.3.1", "axios": "1.1.3", "bcryptjs": "2.4.3", "bee-queue": "1.5.0", "big.js": "6.2.1", "brace": "0.11.1", "bufferutil": "4.0.8", "cheerio": "1.0.0-rc.12", "cobe": "0.6.3", "codeflask": "1.4.1", "component-emitter": "1.3.0", "compression": "1.7.4", "connect-history-api-fallback": "2.0.0", "cors": "2.8.5", "decompress": "4.2.1", "deep-diff": "1.0.2", "element-resize-detector": "1.2.4", "element-ui": "2.15.14", "esbuild": "0.20.1", "exceljs": "4.3.0", "express": "4.18.2", "faker": "5.5.3", "fast-copy": "3.0.2", "fast-csv": "4.3.6", "feathers-hooks-common": "6.1.5", "feathers-profiler": "0.1.5", "file-type": "16.5.4", "fs-extra": "10.1.0", "fusioncharts": "3.20.0", "getmac": "5.20.0", "handlebars": "4.7.8", "helmet": "4.6.0", "highcharts": "10.3.3", "html-to-text": "9.0.5", "html2canvas": "1.4.1", "ioredis": "5.3.2", "javascript-time-ago": "2.5.9", "jquery": "3.7.0", "jsbarcode": "3.11.5", "jsep": "1.3.8", "jspdf": "2.5.1", "knex": "2.5.1", "knex-schema-inspector": "3.0.1", "lodash": "^4.17.21", "lru-cache": "10.2.0", "luxon": "3.4.2", "masonry-layout": "4.2.2", "microtime": "3.1.1", "mime": "3.0.0", "mjml": "4.14.1", "monaco-editor": "0.41.0", "mongodb": "5.9.2", "mousetrap": "1.6.5", "multer": "1.4.5-lts.1", "node-sql-parser": "5.3.1", "node-ssh": "13.1.0", "nodemailer": "6.9.4", "notepack.io": "2.3.0", "object-hash": "3.0.0", "pdf-lib": "1.17.1", "pdfmake": "0.2.7", "perfect-scrollbar": "1.5.6", "pg": "8.11.3", "pg-cursor": "2.10.3", "pikaso": "2.8.0", "pluralize": "8.0.0", "print-js": "1.6.0", "puppeteer": "21.9.0", "qrcode": "1.5.3", "qs": "6.11.2", "quill": "1.3.7", "quill-delta": "5.1.0", "rate-limiter-flexible": "2.4.2", "react": "18.2.0", "react-dom": "18.2.0", "reactflow": "11.8.3", "request": "2.88.2", "resize-observer-polyfill": "1.5.1", "rich-markdown-editor": "11.21.3", "rrule": "2.7.2", "sharp": "0.32.5", "shelljs": "0.8.5", "shopify-api-node": "^3.15.0", "sift": "17.1.3", "soap": "0.45.0", "socket.io": "4.5.3", "socket.io-client": "4.5.3", "sql-formatter": "15.4.0", "stripe": "13.3.0", "styled-components": "6.0.7", "traverse": "0.6.7", "ua-parser-js": "1.0.35", "uberproto": "2.0.6", "utf-8-validate": "6.0.3", "uuid": "9.0.0", "vm2": "3.9.19", "vue": "2.6.14", "vue-class-component": "7.2.6", "vue-deepset": "0.6.3", "vue-easy-dnd": "1.22.0", "vue-property-decorator": "9.1.2", "vue-router": "3.6.5", "vuex": "3.6.2", "whois-ux": "0.0.2", "winston": "3.10.0", "ws": "8.16.0", "xml-stream": "0.4.5", "xml2js": "0.6.2", "yup": "0.32.11"}, "devDependencies": {"assets-webpack-plugin": "7.1.1", "autoprefixer": "10.4.15", "chalk": "4.1.2", "commander": "7.2.0", "css-loader": "6.8.1", "del": "6.1.1", "esbuild-loader": "4.1.0", "mini-css-extract-plugin": "2.7.6", "nodemon": "3.1.0", "postcss-loader": "7.3.3", "prettier": "2.8.7", "raw-loader": "4.0.2", "sass": "1.66.1", "sass-loader": "13.3.2", "source-map-support": "0.5.21", "vue-loader": "15.10.1", "vue-style-loader": "4.1.3", "vue-template-compiler": "2.6.14", "webpack": "5.88.2", "webpack-bundle-analyzer": "4.9.0", "webpack-dev-middleware": "6.1.1", "webpack-hot-middleware": "2.25.4", "webpack-node-externals": "3.0.0", "worker-loader": "3.0.8"}, "browserslist": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 edge version"], "resolutions": {"unicode-properties": "1.3.1"}}